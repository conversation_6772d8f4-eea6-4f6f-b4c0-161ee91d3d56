#pragma once
#include <memory>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>
#include <array>
#include <chrono>

// Forward declaration
class NSMySQLConnection;

// MariaDB 연결 풀 - 스레드 안전성 개선 버전
// 필수 규칙: 같은 CID는 항상 같은 DB 커넥션 사용
class NSMySQLConnectionPool
{
public:
    NSMySQLConnectionPool();
    NSMySQLConnectionPool(const std::string& host, int port, const std::string& dbName,
                         const std::string& user, const std::string& password);
    ~NSMySQLConnectionPool();

    // 복사/이동 방지
    NSMySQLConnectionPool(const NSMySQLConnectionPool&) = delete;
    NSMySQLConnectionPool& operator=(const NSMySQLConnectionPool&) = delete;
    NSMySQLConnectionPool(NSMySQLConnectionPool&&) = delete;
    NSMySQLConnectionPool& operator=(NSMySQLConnectionPool&&) = delete;

    // 초기화/종료
    bool Initialize(int databaseType, int shardId);
    void Finalize();

    // 연결 획듍/반환 - 스레드 인덱스 기반 (CID 라우팅용)
    std::shared_ptr<NSMySQLConnection> GetConnection(int threadIndex);
    void ReturnConnection(std::shared_ptr<NSMySQLConnection> conn, int threadIndex);
    
    // 기존 API 호환성 (랜덤 스레드 선택)
    std::shared_ptr<NSMySQLConnection> GetConnection();
    void ReturnConnection(std::shared_ptr<NSMySQLConnection> conn);

    // 설정
    void SetMinConnections(int count) { m_minConnections = count; }
    void SetMaxConnections(int count) { m_maxConnections = count; }
    
    // 연결 정보 추가
    bool AddConnectionInfo(const std::string& host, int port, const std::string& dbName, 
                          const std::string& user, const std::string& password);
    
    // 재연결
    void Reconnect();
    
    // 상태 조회
    int GetActiveConnections() const { return m_activeConnections.load(); }
    int GetTotalConnections() const { return m_totalConnections.load(); }
    int GetActiveConnectionCount() const { return m_activeConnections.load(); }
    int GetTotalConnectionCount() const { return m_totalConnections.load(); }
    bool IsHealthy() const;

private:
    // 연결 생성
    std::shared_ptr<NSMySQLConnection> CreateConnection();
    
    // 연결 정보 로드
    bool LoadConnectionInfo();
    
    // 연결 검증
    bool ValidateConnection(const std::shared_ptr<NSMySQLConnection>& conn);
    
    // 스레드별 전용 커넥션 정리
    void CleanupIdleConnections();

    // 스레드별 전용 커넥션 관리
    struct ThreadConnection {
        std::shared_ptr<NSMySQLConnection> connection;
        std::chrono::steady_clock::time_point lastUsed;
        std::atomic<int64_t> queryCount{0};
        std::atomic<bool> inUse{false};
        std::mutex mutex; // 개별 연결 보호
    };
    
    static constexpr int MAX_THREADS = 64;
    static constexpr auto IDLE_CONNECTION_TIMEOUT = std::chrono::minutes(5);
    
    // 스레드별 전용 연결 (고정 크기 배열)
    std::array<ThreadConnection, MAX_THREADS> m_threadConnections;

    // 공용 연결 풀 (초과 요청 처리용)
    std::queue<std::shared_ptr<NSMySQLConnection>> m_sharedPool;
    mutable std::mutex m_sharedPoolMutex;
    std::condition_variable m_sharedPoolCv;

    // 설정
    int m_databaseType = 0;
    int m_shardId = 0;
    std::atomic<int> m_minConnections{5};
    std::atomic<int> m_maxConnections{20};
    
    // 통계
    std::atomic<int> m_activeConnections{0};
    std::atomic<int> m_totalConnections{0};
    std::atomic<int> m_nextThreadIndex{0};
    std::atomic<int64_t> m_totalQueries{0};
    std::atomic<int64_t> m_failedConnections{0};
    
    // 연결 정보
    std::string m_host;
    int m_port = 3306;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    
    // 상태
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_shutting_down{false};
    
    // 정리 스레드
    std::thread m_cleanupThread;
    std::condition_variable m_cleanupCv;
    std::mutex m_cleanupMutex;
};